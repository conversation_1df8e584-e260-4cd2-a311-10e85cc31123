<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Garden;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class RoleBasedAccessTest extends TestCase
{
    use RefreshDatabase;

    protected Garden $garden;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test garden
        $this->garden = Garden::create([
            'name' => 'Test Garden',
            'description' => 'A test garden',
            'address' => 'Test Address',
            'latitude' => 40.7128,
            'longitude' => -74.0060,
            'settings' => json_encode(['timezone' => 'UTC']),
            'is_active' => true,
        ]);
    }

    public function test_admin_can_access_user_management(): void
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'garden_id' => $this->garden->id,
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)->get('/admin/users');
        $response->assertStatus(200);
    }

    public function test_editor_cannot_access_user_management(): void
    {
        $editor = User::create([
            'name' => 'Editor User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'editor',
            'garden_id' => $this->garden->id,
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($editor)->get('/admin/users');
        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_protected_routes(): void
    {
        $response = $this->get('/dashboard');
        $response->assertRedirect('/login');

        $response = $this->get('/admin/users');
        $response->assertRedirect('/login');
    }

    public function test_user_role_methods(): void
    {
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'garden_id' => $this->garden->id,
            'email_verified_at' => now(),
        ]);

        $editor = User::create([
            'name' => 'Editor User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'editor',
            'garden_id' => $this->garden->id,
            'email_verified_at' => now(),
        ]);

        $viewer = User::create([
            'name' => 'Viewer User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'viewer',
            'garden_id' => $this->garden->id,
            'email_verified_at' => now(),
        ]);

        // Test admin role methods
        $this->assertTrue($admin->isAdmin());
        $this->assertFalse($admin->isEditor());
        $this->assertTrue($admin->canManagePlants());

        // Test editor role methods
        $this->assertFalse($editor->isAdmin());
        $this->assertTrue($editor->isEditor());
        $this->assertTrue($editor->canManagePlants());

        // Test viewer role methods
        $this->assertFalse($viewer->isAdmin());
        $this->assertFalse($viewer->isEditor());
        $this->assertFalse($viewer->canManagePlants());
    }
}
